{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "ccedf19bea4c95842bfd143718dfe3ae", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5678191ff34950123f3a46c8651b23d68453e717a78fa0860934a997124f9ec9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3e9ef2b6dcb7412d7f32fbe26fec4204c3242016048fad0bb33f2480d5406954"}}}, "sortedMiddleware": ["/"], "functions": {}}